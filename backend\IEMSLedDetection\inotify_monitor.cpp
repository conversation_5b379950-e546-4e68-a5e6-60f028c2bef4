#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <atomic>
#include <chrono>
#include <unistd.h>
#include <sys/inotify.h>
#include <sys/stat.h>
#include <cstring>
#include <signal.h>

// 使用 inotify 监控串口设备文件的访问事件
class InotifySerialMonitor {
private:
    std::vector<std::string> serialPorts;
    std::atomic<bool> running;
    int inotifyFd;
    std::vector<int> watchDescriptors;

public:
    InotifySerialMonitor() : running(false), inotifyFd(-1) {
        serialPorts = {
            "/dev/ttymxc1",
            "/dev/ttymxc3", 
            "/dev/ttymxc6",
            "/dev/ttymxc7"
        };
    }

    ~InotifySerialMonitor() {
        stop();
    }

    bool start() {
        if (running) {
            std::cout << "监控已经在运行中" << std::endl;
            return false;
        }

        // 创建 inotify 实例
        inotifyFd = inotify_init();
        if (inotifyFd == -1) {
            std::cout << "无法创建 inotify 实例: " << strerror(errno) << std::endl;
            return false;
        }

        std::cout << "=== inotify 串口监控程序 ===" << std::endl;
        std::cout << "完全非侵入式，不影响串口数据传输" << std::endl;
        std::cout << "监控的串口设备:" << std::endl;

        // 为每个串口设备添加监控
        for (size_t i = 0; i < serialPorts.size(); ++i) {
            const std::string& port = serialPorts[i];
            
            // 检查设备是否存在
            struct stat statBuf;
            if (stat(port.c_str(), &statBuf) != 0) {
                std::cout << "  [" << i << "] " << port << " - 不存在，跳过" << std::endl;
                watchDescriptors.push_back(-1);
                continue;
            }

            // 添加监控：访问、修改、打开、关闭事件
            int wd = inotify_add_watch(inotifyFd, port.c_str(), 
                                     IN_ACCESS | IN_MODIFY | IN_OPEN | IN_CLOSE);
            
            if (wd == -1) {
                std::cout << "  [" << i << "] " << port << " - 无法添加监控: " 
                          << strerror(errno) << std::endl;
                watchDescriptors.push_back(-1);
            } else {
                std::cout << "  [" << i << "] " << port << " - 监控已添加 (wd=" << wd << ")" << std::endl;
                watchDescriptors.push_back(wd);
            }
        }

        running = true;

        // 启动监控线程
        std::thread monitorThread([this]() {
            monitorEvents();
        });
        monitorThread.detach();

        return true;
    }

    void stop() {
        if (!running) return;

        running = false;

        // 移除所有监控
        for (int wd : watchDescriptors) {
            if (wd != -1) {
                inotify_rm_watch(inotifyFd, wd);
            }
        }
        watchDescriptors.clear();

        // 关闭 inotify 文件描述符
        if (inotifyFd != -1) {
            close(inotifyFd);
            inotifyFd = -1;
        }

        std::cout << "inotify 监控已停止" << std::endl;
    }

    bool isRunning() const { return running; }

private:
    void monitorEvents() {
        const size_t bufferSize = 4096;
        char buffer[bufferSize];

        std::cout << "开始监控串口设备事件..." << std::endl;
        std::cout << "当有程序访问串口时会显示相关信息" << std::endl;
        std::cout << "----------------------------------------" << std::endl;

        while (running) {
            // 设置超时，避免阻塞
            fd_set readfds;
            FD_ZERO(&readfds);
            FD_SET(inotifyFd, &readfds);

            struct timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;

            int result = select(inotifyFd + 1, &readfds, nullptr, nullptr, &timeout);
            
            if (result < 0) {
                if (errno == EINTR) continue;
                std::cout << "select 错误: " << strerror(errno) << std::endl;
                break;
            }

            if (result == 0) {
                // 超时，继续循环
                continue;
            }

            // 读取事件
            ssize_t length = read(inotifyFd, buffer, bufferSize);
            if (length < 0) {
                if (errno == EINTR) continue;
                std::cout << "读取 inotify 事件错误: " << strerror(errno) << std::endl;
                break;
            }

            // 处理事件
            size_t offset = 0;
            while (offset < static_cast<size_t>(length)) {
                struct inotify_event* event = 
                    reinterpret_cast<struct inotify_event*>(buffer + offset);
                
                processEvent(event);
                
                offset += sizeof(struct inotify_event) + event->len;
            }
        }
    }

    void processEvent(struct inotify_event* event) {
        // 找到对应的串口设备
        int portIndex = -1;
        for (size_t i = 0; i < watchDescriptors.size(); ++i) {
            if (watchDescriptors[i] == event->wd) {
                portIndex = i;
                break;
            }
        }

        if (portIndex == -1) {
            return; // 未找到对应设备
        }

        const std::string& portPath = serialPorts[portIndex];
        std::string eventType;

        // 解析事件类型
        if (event->mask & IN_ACCESS) {
            eventType += "访问 ";
        }
        if (event->mask & IN_MODIFY) {
            eventType += "修改 ";
        }
        if (event->mask & IN_OPEN) {
            eventType += "打开 ";
        }
        if (event->mask & IN_CLOSE) {
            eventType += "关闭 ";
        }

        if (!eventType.empty()) {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            auto tm = *std::localtime(&time_t);
            
            printf("[%02d:%02d:%02d] 串口 %s 事件: %s\n", 
                   tm.tm_hour, tm.tm_min, tm.tm_sec,
                   portPath.c_str(), eventType.c_str());

            // 控制LED
            controlLED(portIndex, true);
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            controlLED(portIndex, false);
        }
    }

    void controlLED(int portIndex, bool turnOn) {
        std::cout << "LED控制 - 端口索引: " << portIndex 
                  << ", 状态: " << (turnOn ? "点亮" : "熄灭") << std::endl;
        
        // 实际LED控制代码（需要根据硬件调整）
        /*
        std::string ledPath = "/sys/class/leds/led" + std::to_string(portIndex) + "/brightness";
        std::ofstream ledFile(ledPath);
        if (ledFile.is_open()) {
            ledFile << (turnOn ? "1" : "0");
            ledFile.close();
        }
        */
    }
};

// 全局变量用于信号处理
std::unique_ptr<InotifySerialMonitor> g_monitor;

// 信号处理函数
void signalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在退出..." << std::endl;
    if (g_monitor) {
        g_monitor->stop();
    }
    exit(0);
}

int main() {
    std::cout << "=== inotify 串口监控程序 ===" << std::endl;
    std::cout << "版本: 1.0" << std::endl;
    std::cout << "特点: 使用 inotify 监控，完全不影响串口数据传输" << std::endl;
    std::cout << "=========================================" << std::endl;

    // 注册信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    // 创建监控器
    g_monitor = std::make_unique<InotifySerialMonitor>();

    // 启动监控
    if (!g_monitor->start()) {
        std::cout << "启动监控失败" << std::endl;
        return 1;
    }

    // 主循环
    std::cout << "\n程序运行中... 按 Ctrl+C 退出" << std::endl;
    std::cout << "此程序使用 inotify 监控，完全不会影响串口数据传输" << std::endl;

    while (g_monitor->isRunning()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    return 0;
}
