#include "SerialMonitor.h"
#include <iostream>
#include <string>
#include <cstdio>
#include <chrono>
#include <thread>
#include <unistd.h>
#include <sys/inotify.h>
#include <sys/select.h>

SerialMonitor::SerialMonitor() : running(false) {
    // 要监控的串口列表
    serialPorts = {
        "/dev/ttymxc6",  // LED1
        "/dev/ttymxc7",  // LED2
        "/dev/ttymxc1",  // LED3
        "/dev/ttymxc3"   // LED4
    };
}

SerialMonitor::~SerialMonitor() {
    stop();
}

// 使用 inotify 实时监控
void SerialMonitor::monitorPort(const std::string& portPath, int portIndex) {
    int inotifyFd = inotify_init();
    if (inotifyFd == -1) {
        std::cout << "无法创建 inotify 实例: " << portPath << std::endl;
        return;
    }

    int wd = inotify_add_watch(inotifyFd, portPath.c_str(), IN_ACCESS | IN_MODIFY);
    if (wd == -1) {
        std::cout << "无法监控 " << portPath << std::endl;
        close(inotifyFd);
        return;
    }

    std::cout << "开始监控: " << portPath << std::endl;

    char buffer[4096];
    while (running) {
        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(inotifyFd, &readfds);

        struct timeval timeout;
        timeout.tv_sec = 1;
        timeout.tv_usec = 0;

        int result = select(inotifyFd + 1, &readfds, nullptr, nullptr, &timeout);
        if (result > 0 && FD_ISSET(inotifyFd, &readfds)) {
            ssize_t length = read(inotifyFd, buffer, sizeof(buffer));
            if (length > 0) {
                int ledNumber = portIndex + 1; // LED编号从1开始
                std::cout << "检测到串口 " << portPath << " 有数据交互，LED" << ledNumber << " 亮灭交替" << std::endl;

                // 亮灭交替效果
                controlLED(portIndex, true);   // 先点亮
                std::this_thread::sleep_for(std::chrono::milliseconds(200)); // 亮200ms
                controlLED(portIndex, false);  // 再熄灭
            }
        }
    }

    inotify_rm_watch(inotifyFd, wd);
    close(inotifyFd);
}

// 启动监控
void SerialMonitor::start() {
    if (running) {
        return;
    }
    
    running = true;
    std::cout << "启动串口监控..." << std::endl;
    
    // 为每个串口创建监控线程
    for (size_t i = 0; i < serialPorts.size(); ++i) {
        std::cout << "监控串口: " << serialPorts[i] << std::endl;
        
        monitorThreads.emplace_back([this, i]() {
            monitorPort(serialPorts[i], i);
        });
    }
}

// 停止监控
void SerialMonitor::stop() {
    if (!running) return;
    
    running = false;
    
    for (auto& thread : monitorThreads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    
    monitorThreads.clear();
}

bool SerialMonitor::isRunning() const {
    return running;
}

// LED控制函数
void SerialMonitor::controlLED(int portIndex, bool turnOn) {
    int ledNumber = portIndex + 1; // LED编号从1开始
    std::string ledPath = "/sys/class/leds/led" + std::to_string(ledNumber) + "/brightness";

    // 使用echo命令写入LED控制文件
    std::string command = "echo " + std::to_string(turnOn ? 1 : 0) + " > " + ledPath;
    int result = system(command.c_str());

    if (result == 0) {
        std::cout << "LED" << ledNumber << " " << (turnOn ? "亮" : "灭") << std::endl;
    } else {
        std::cout << "无法控制LED" << ledNumber << " (路径: " << ledPath << ")" << std::endl;
    }
}
