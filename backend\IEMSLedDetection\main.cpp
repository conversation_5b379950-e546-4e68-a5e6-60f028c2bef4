#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <atomic>
#include <chrono>
#include <unistd.h>
#include <sys/stat.h>
#include <signal.h>

class SerialMonitor {
private:
    std::vector<std::string> serialPorts;
    std::vector<std::thread> monitorThreads;
    std::atomic<bool> running;

public:
    SerialMonitor() : running(false) {
        // 要监控的串口列表
        serialPorts = {
            "/dev/ttymxc1",
            "/dev/ttymxc3", 
            "/dev/ttymxc6",
            "/dev/ttymxc7"
        };
    }

    ~SerialMonitor() {
        stop();
    }

    // 监控设备文件的访问时间变化（非侵入式）
    void monitorPort(const std::string& portPath, int portIndex) {
        struct stat lastStat;
        bool hasLastStat = false;
        
        while (running) {
            struct stat currentStat;
            if (stat(portPath.c_str(), &currentStat) == 0) {
                if (hasLastStat) {
                    // 检查访问时间或修改时间是否变化
                    if (currentStat.st_atime != lastStat.st_atime || 
                        currentStat.st_mtime != lastStat.st_mtime) {
                        std::cout << "检测到串口 " << portPath << " 有数据交互" << std::endl;
                    }
                }
                lastStat = currentStat;
                hasLastStat = true;
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }

    // 启动监控
    void start() {
        if (running) {
            return;
        }
        
        running = true;
        std::cout << "启动串口监控..." << std::endl;
        
        // 为每个串口创建监控线程
        for (size_t i = 0; i < serialPorts.size(); ++i) {
            std::cout << "监控串口: " << serialPorts[i] << std::endl;
            
            monitorThreads.emplace_back([this, i]() {
                monitorPort(serialPorts[i], i);
            });
        }
    }

    // 停止监控
    void stop() {
        if (!running) return;
        
        running = false;
        
        for (auto& thread : monitorThreads) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        
        monitorThreads.clear();
    }

    bool isRunning() const { return running; }
};

// 全局变量用于信号处理
std::unique_ptr<SerialMonitor> g_monitor;

// 信号处理函数
void signalHandler(int signal) {
    std::cout << "\n收到退出信号，正在停止..." << std::endl;
    if (g_monitor) {
        g_monitor->stop();
    }
    exit(0);
}

int main() {
    std::cout << "串口监控程序启动" << std::endl;
    
    // 注册信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    // 创建监控器
    g_monitor = std::make_unique<SerialMonitor>();

    // 启动监控
    g_monitor->start();

    std::cout << "程序运行中，按 Ctrl+C 退出" << std::endl;

    // 主循环
    while (g_monitor->isRunning()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    return 0;
}
