#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <atomic>
#include <chrono>
#include <fstream>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h>
#include <sys/select.h>
#include <sys/stat.h>
#include <cstring>
#include <signal.h>
#include <sys/inotify.h>
#include <dirent.h>

// 监控模式枚举
enum class MonitorMode {
    NON_INVASIVE,    // 非侵入式：只检查文件描述符状态，不读取数据
    PROCESS_MONITOR, // 进程监控：监控使用串口的进程
    INOTIFY_MONITOR, // 文件系统监控：使用inotify监控设备文件
    INVASIVE         // 侵入式：直接读取串口数据（可能影响其他程序）
};

class SerialPortMonitor {
private:
    std::vector<std::string> serialPorts;
    std::vector<std::thread> monitorThreads;
    std::atomic<bool> running;
    MonitorMode mode;

    // LED控制相关（预留接口）
    struct LEDController {
        std::string ledPath;
        int portIndex;

        LEDController(const std::string& path, int index)
            : ledPath(path), portIndex(index) {}
    };

    std::vector<LEDController> ledControllers;

public:
    SerialPortMonitor(MonitorMode monitorMode = MonitorMode::NON_INVASIVE)
        : running(false), mode(monitorMode) {
        // 初始化要监控的串口列表（写死在程序中）
        serialPorts = {
            "/dev/ttymxc6",
            "/dev/ttymxc7",
            "/dev/ttymxc1",
            "/dev/ttymxc3"
        };

        // 初始化LED控制器（预留，对应每个串口）
        for (size_t i = 0; i < serialPorts.size(); ++i) {
            // 这里预留LED路径，实际使用时需要根据硬件修改
            std::string ledPath = "/sys/class/leds/led" + std::to_string(i) + "/brightness";
            ledControllers.emplace_back(ledPath, i);
        }

        std::cout << "监控模式: ";
        switch(mode) {
            case MonitorMode::NON_INVASIVE:
                std::cout << "非侵入式（推荐）- 不会影响其他程序" << std::endl;
                break;
            case MonitorMode::PROCESS_MONITOR:
                std::cout << "进程监控 - 监控使用串口的进程" << std::endl;
                break;
            case MonitorMode::INOTIFY_MONITOR:
                std::cout << "文件系统监控 - 使用inotify" << std::endl;
                break;
            case MonitorMode::INVASIVE:
                std::cout << "侵入式 - 可能影响其他程序使用串口" << std::endl;
                break;
        }
    }

    ~SerialPortMonitor() {
        stop();
    }

    // 检查串口是否存在
    bool checkSerialPortExists(const std::string& portPath) {
        struct stat buffer;
        return (stat(portPath.c_str(), &buffer) == 0);
    }

    // 监控单个串口的数据活动
    void monitorSerialPort(const std::string& portPath, int portIndex) {
        std::cout << "开始监控串口: " << portPath << " (索引: " << portIndex << ")" << std::endl;

        while (running) {
            // 检查串口是否存在
            if (!checkSerialPortExists(portPath)) {
                std::cout << "串口 " << portPath << " 不存在，等待..." << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(5));
                continue;
            }

            switch (mode) {
                case MonitorMode::NON_INVASIVE:
                    monitorNonInvasive(portPath, portIndex);
                    break;
                case MonitorMode::PROCESS_MONITOR:
                    monitorByProcess(portPath, portIndex);
                    break;
                case MonitorMode::INOTIFY_MONITOR:
                    monitorByInotify(portPath, portIndex);
                    break;
                case MonitorMode::INVASIVE:
                    monitorInvasive(portPath, portIndex);
                    break;
            }

            if (running) {
                std::this_thread::sleep_for(std::chrono::seconds(2));
            }
        }

        std::cout << "停止监控串口: " << portPath << std::endl;
    }

    // 非侵入式监控：只检查文件描述符状态，不读取数据
    void monitorNonInvasive(const std::string& portPath, int portIndex) {
        std::cout << "使用非侵入式监控: " << portPath << std::endl;

        while (running && checkSerialPortExists(portPath)) {
            // 尝试以只读方式打开，但不读取数据
            int fd = open(portPath.c_str(), O_RDONLY | O_NONBLOCK);
            if (fd < 0) {
                std::this_thread::sleep_for(std::chrono::seconds(5));
                continue;
            }

            while (running) {
                fd_set readfds;
                FD_ZERO(&readfds);
                FD_SET(fd, &readfds);

                struct timeval timeout;
                timeout.tv_sec = 1;
                timeout.tv_usec = 0;

                int result = select(fd + 1, &readfds, nullptr, nullptr, &timeout);

                if (result < 0) {
                    if (errno == EINTR) continue;
                    break;
                }

                if (result > 0 && FD_ISSET(fd, &readfds)) {
                    // 检测到有数据活动
                    std::cout << "检测到串口 " << portPath << " 有数据活动（非侵入式）" << std::endl;

                    // 读取少量数据以清空缓冲区，避免select持续触发
                    // 但不处理数据内容，尽量减少对其他程序的影响
                    char tempBuffer[1];
                    ssize_t bytesRead = read(fd, tempBuffer, 1);
                    if (bytesRead > 0) {
                        std::cout << "读取了 " << bytesRead << " 字节用于清空缓冲区" << std::endl;
                    }

                    controlLED(portIndex, true);
                    std::this_thread::sleep_for(std::chrono::milliseconds(500));
                    controlLED(portIndex, false);

                    // 短暂延迟避免频繁触发
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }

                if (!checkSerialPortExists(portPath)) {
                    break;
                }
            }

            close(fd);
            break;
        }
    }

    // 通过监控进程来检测串口使用情况
    void monitorByProcess(const std::string& portPath, int portIndex) {
        std::cout << "使用进程监控: " << portPath << std::endl;

        std::string lastProcessList;

        while (running && checkSerialPortExists(portPath)) {
            // 使用lsof命令检查哪些进程在使用串口
            std::string command = "lsof " + portPath + " 2>/dev/null";
            FILE* pipe = popen(command.c_str(), "r");

            if (pipe) {
                char buffer[256];
                std::string currentProcessList;

                while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
                    currentProcessList += buffer;
                }
                pclose(pipe);

                // 如果进程列表发生变化，说明有程序开始或停止使用串口
                if (!currentProcessList.empty() && currentProcessList != lastProcessList) {
                    std::cout << "检测到串口 " << portPath << " 使用状态变化" << std::endl;

                    controlLED(portIndex, true);
                    std::this_thread::sleep_for(std::chrono::milliseconds(500));
                    controlLED(portIndex, false);

                    lastProcessList = currentProcessList;
                }
            }

            std::this_thread::sleep_for(std::chrono::seconds(2));
        }
    }

    // 使用inotify监控设备文件
    void monitorByInotify(const std::string& portPath, int portIndex) {
        std::cout << "使用inotify监控: " << portPath << std::endl;

        // 简化实现：监控设备文件的访问时间变化
        struct stat lastStat;
        bool hasLastStat = false;

        while (running && checkSerialPortExists(portPath)) {
            struct stat currentStat;
            if (stat(portPath.c_str(), &currentStat) == 0) {
                if (hasLastStat) {
                    // 检查访问时间是否变化
                    if (currentStat.st_atime != lastStat.st_atime) {
                        std::cout << "检测到串口 " << portPath << " 被访问" << std::endl;

                        controlLED(portIndex, true);
                        std::this_thread::sleep_for(std::chrono::milliseconds(500));
                        controlLED(portIndex, false);
                    }
                }
                lastStat = currentStat;
                hasLastStat = true;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }

    // 侵入式监控：直接读取串口数据（原来的方法）
    void monitorInvasive(const std::string& portPath, int portIndex) {
        std::cout << "使用侵入式监控: " << portPath << " (警告：可能影响其他程序)" << std::endl;

        int fd = open(portPath.c_str(), O_RDONLY | O_NONBLOCK);
        if (fd < 0) {
            std::cout << "无法打开串口 " << portPath << ": " << strerror(errno) << std::endl;
            return;
        }

        // 配置串口参数
        struct termios tty;
        if (tcgetattr(fd, &tty) == 0) {
            cfmakeraw(&tty);
            tcsetattr(fd, TCSANOW, &tty);
        }

        while (running) {
            fd_set readfds;
            FD_ZERO(&readfds);
            FD_SET(fd, &readfds);

            struct timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;

            int result = select(fd + 1, &readfds, nullptr, nullptr, &timeout);

            if (result < 0) {
                if (errno == EINTR) continue;
                break;
            }

            if (result > 0 && FD_ISSET(fd, &readfds)) {
                char buffer[1024];
                ssize_t bytesRead = read(fd, buffer, sizeof(buffer));

                if (bytesRead > 0) {
                    std::cout << "检测到串口 " << portPath << " 有数据传输，字节数: "
                              << bytesRead << std::endl;

                    controlLED(portIndex, true);
                    std::this_thread::sleep_for(std::chrono::milliseconds(500));
                    controlLED(portIndex, false);
                } else if (bytesRead < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
                    break;
                }
            }

            if (!checkSerialPortExists(portPath)) {
                break;
            }
        }

        close(fd);
    }

    // LED控制函数（预留实现）
    void controlLED(int portIndex, bool turnOn) {
        if (portIndex < 0 || portIndex >= static_cast<int>(ledControllers.size())) {
            return;
        }

        const auto& ledController = ledControllers[portIndex];

        // 这里是LED控制的预留实现
        // 实际使用时需要根据具体硬件平台修改
        std::cout << "LED控制 - 端口索引: " << portIndex
                  << ", LED路径: " << ledController.ledPath
                  << ", 状态: " << (turnOn ? "点亮" : "熄灭") << std::endl;

        // 示例：通过sysfs控制LED（需要root权限）
        /*
        std::ofstream ledFile(ledController.ledPath);
        if (ledFile.is_open()) {
            ledFile << (turnOn ? "1" : "0");
            ledFile.close();
        }
        */
    }

    // 启动监控
    void start() {
        if (running) {
            std::cout << "监控已经在运行中" << std::endl;
            return;
        }

        running = true;

        std::cout << "启动串口监控程序..." << std::endl;
        std::cout << "监控的串口列表:" << std::endl;

        // 为每个串口创建监控线程
        for (size_t i = 0; i < serialPorts.size(); ++i) {
            std::cout << "  [" << i << "] " << serialPorts[i] << std::endl;

            monitorThreads.emplace_back([this, i]() {
                monitorSerialPort(serialPorts[i], i);
            });
        }

        std::cout << "所有监控线程已启动" << std::endl;
    }

    // 停止监控
    void stop() {
        if (!running) {
            return;
        }

        std::cout << "正在停止串口监控..." << std::endl;
        running = false;

        // 等待所有线程结束
        for (auto& thread : monitorThreads) {
            if (thread.joinable()) {
                thread.join();
            }
        }

        monitorThreads.clear();
        std::cout << "串口监控已停止" << std::endl;
    }

    // 获取监控状态
    bool isRunning() const {
        return running;
    }

    // 添加串口到监控列表
    void addSerialPort(const std::string& portPath) {
        serialPorts.push_back(portPath);
        std::string ledPath = "/sys/class/leds/led" + std::to_string(serialPorts.size() - 1) + "/brightness";
        ledControllers.emplace_back(ledPath, serialPorts.size() - 1);
    }

    // 显示当前监控的串口
    void showMonitoredPorts() {
        std::cout << "当前监控的串口:" << std::endl;
        for (size_t i = 0; i < serialPorts.size(); ++i) {
            std::cout << "  [" << i << "] " << serialPorts[i]
                      << " -> LED: " << ledControllers[i].ledPath << std::endl;
        }
    }
};

// 全局变量用于信号处理
std::unique_ptr<SerialPortMonitor> g_monitor;

// 信号处理函数
void signalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在退出..." << std::endl;
    if (g_monitor) {
        g_monitor->stop();
    }
    exit(0);
}

void printUsage(const char* programName) {
    std::cout << "用法: " << programName << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -m, --mode <模式>    监控模式:" << std::endl;
    std::cout << "                       0 = 非侵入式（默认，推荐）" << std::endl;
    std::cout << "                       1 = 进程监控" << std::endl;
    std::cout << "                       2 = 文件系统监控" << std::endl;
    std::cout << "                       3 = 侵入式（可能影响其他程序）" << std::endl;
    std::cout << "  -h, --help           显示此帮助信息" << std::endl;
    std::cout << std::endl;
    std::cout << "监控模式说明:" << std::endl;
    std::cout << "  非侵入式: 只检测数据活动，不读取数据，不影响其他程序" << std::endl;
    std::cout << "  进程监控: 通过监控使用串口的进程来检测活动" << std::endl;
    std::cout << "  文件系统监控: 监控设备文件的访问时间变化" << std::endl;
    std::cout << "  侵入式: 直接读取串口数据，可能影响其他程序" << std::endl;
}

int main(int argc, char* argv[]) {
    std::cout << "=== ARM32 Linux 串口监控与LED控制程序 ===" << std::endl;
    std::cout << "版本: 1.0" << std::endl;
    std::cout << "功能: 监控RS485/RS232串口数据传输并控制LED指示" << std::endl;
    std::cout << "=========================================" << std::endl;

    MonitorMode mode = MonitorMode::NON_INVASIVE;  // 默认使用非侵入式

    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "-h" || arg == "--help") {
            printUsage(argv[0]);
            return 0;
        } else if (arg == "-m" || arg == "--mode") {
            if (i + 1 < argc) {
                int modeValue = std::atoi(argv[i + 1]);
                switch (modeValue) {
                    case 0: mode = MonitorMode::NON_INVASIVE; break;
                    case 1: mode = MonitorMode::PROCESS_MONITOR; break;
                    case 2: mode = MonitorMode::INOTIFY_MONITOR; break;
                    case 3: mode = MonitorMode::INVASIVE; break;
                    default:
                        std::cout << "错误: 无效的监控模式 " << modeValue << std::endl;
                        printUsage(argv[0]);
                        return 1;
                }
                i++; // 跳过模式参数
            } else {
                std::cout << "错误: -m/--mode 需要一个参数" << std::endl;
                printUsage(argv[0]);
                return 1;
            }
        } else {
            std::cout << "错误: 未知参数 " << arg << std::endl;
            printUsage(argv[0]);
            return 1;
        }
    }

    // 注册信号处理
    signal(SIGINT, signalHandler);   // Ctrl+C
    signal(SIGTERM, signalHandler);  // 终止信号

    // 创建监控器
    g_monitor = std::make_unique<SerialPortMonitor>(mode);

    // 显示要监控的串口
    g_monitor->showMonitoredPorts();

    // 启动监控
    g_monitor->start();

    // 主循环
    std::cout << "\n程序运行中... 按 Ctrl+C 退出" << std::endl;

    while (g_monitor->isRunning()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    return 0;
}