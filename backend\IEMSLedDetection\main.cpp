#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <signal.h>
#include "SerialMonitor.h"

// 全局变量用于信号处理
std::unique_ptr<SerialMonitor> g_monitor;

// 信号处理函数
void signalHandler(int signal) {
    std::cout << "\n收到退出信号，正在停止..." << std::endl;
    if (g_monitor) {
        g_monitor->stop();
    }
    exit(0);
}

int main() {
    std::cout << "inotify串口监控程序启动" << std::endl;

    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    g_monitor = std::make_unique<SerialMonitor>();
    g_monitor->start();

    std::cout << "程序运行中，按 Ctrl+C 退出" << std::endl;

    while (g_monitor->isRunning()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    return 0;
}
