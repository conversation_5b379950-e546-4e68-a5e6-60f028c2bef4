#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <atomic>
#include <chrono>
#include <fstream>
#include <unistd.h>
#include <fcntl.h>
#include <termios.h>
#include <sys/select.h>
#include <sys/stat.h>
#include <cstring>
#include <signal.h>

class SerialPortMonitor {
private:
    std::vector<std::string> serialPorts;
    std::vector<std::thread> monitorThreads;
    std::atomic<bool> running;

    // LED控制相关（预留接口）
    struct LEDController {
        std::string ledPath;
        int portIndex;

        LEDController(const std::string& path, int index)
            : ledPath(path), portIndex(index) {}
    };

    std::vector<LEDController> ledControllers;

public:
    SerialPortMonitor() : running(false) {
        // 初始化要监控的串口列表（写死在程序中）
        serialPorts = {
            "/dev/ttymxc1",
            "/dev/ttymxc2",
            "/dev/ttymxc3",
            "/dev/ttyUSB0"
        };

        // 初始化LED控制器（预留，对应每个串口）
        for (size_t i = 0; i < serialPorts.size(); ++i) {
            // 这里预留LED路径，实际使用时需要根据硬件修改
            std::string ledPath = "/sys/class/leds/led" + std::to_string(i) + "/brightness";
            ledControllers.emplace_back(ledPath, i);
        }
    }

    ~SerialPortMonitor() {
        stop();
    }

    // 检查串口是否存在
    bool checkSerialPortExists(const std::string& portPath) {
        struct stat buffer;
        return (stat(portPath.c_str(), &buffer) == 0);
    }

    // 监控单个串口的数据活动
    void monitorSerialPort(const std::string& portPath, int portIndex) {
        std::cout << "开始监控串口: " << portPath << " (索引: " << portIndex << ")" << std::endl;

        while (running) {
            // 检查串口是否存在
            if (!checkSerialPortExists(portPath)) {
                std::cout << "串口 " << portPath << " 不存在，等待..." << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(5));
                continue;
            }

            int fd = open(portPath.c_str(), O_RDONLY | O_NONBLOCK);
            if (fd < 0) {
                std::cout << "无法打开串口 " << portPath << ": " << strerror(errno) << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(5));
                continue;
            }

            // 配置串口参数
            struct termios tty;
            if (tcgetattr(fd, &tty) != 0) {
                std::cout << "获取串口属性失败 " << portPath << ": " << strerror(errno) << std::endl;
                close(fd);
                std::this_thread::sleep_for(std::chrono::seconds(5));
                continue;
            }

            // 设置为原始模式
            cfmakeraw(&tty);
            tcsetattr(fd, TCSANOW, &tty);

            std::cout << "成功打开串口: " << portPath << std::endl;

            // 监控数据活动
            while (running) {
                fd_set readfds;
                FD_ZERO(&readfds);
                FD_SET(fd, &readfds);

                struct timeval timeout;
                timeout.tv_sec = 1;  // 1秒超时
                timeout.tv_usec = 0;

                int result = select(fd + 1, &readfds, nullptr, nullptr, &timeout);

                if (result < 0) {
                    if (errno == EINTR) continue;  // 被信号中断，继续
                    std::cout << "select错误 " << portPath << ": " << strerror(errno) << std::endl;
                    break;
                }

                if (result > 0 && FD_ISSET(fd, &readfds)) {
                    // 有数据可读
                    char buffer[1024];
                    ssize_t bytesRead = read(fd, buffer, sizeof(buffer));

                    if (bytesRead > 0) {
                        std::cout << "检测到串口 " << portPath << " 有数据传输，字节数: "
                                  << bytesRead << std::endl;

                        // 点亮对应的LED（预留功能）
                        controlLED(portIndex, true);

                        // LED点亮一段时间后熄灭（预留功能）
                        std::this_thread::sleep_for(std::chrono::milliseconds(500));
                        controlLED(portIndex, false);
                    } else if (bytesRead < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
                        std::cout << "读取串口数据错误 " << portPath << ": " << strerror(errno) << std::endl;
                        break;
                    }
                }

                // 检查串口是否仍然存在
                if (!checkSerialPortExists(portPath)) {
                    std::cout << "串口 " << portPath << " 已断开" << std::endl;
                    break;
                }
            }

            close(fd);
            std::cout << "关闭串口: " << portPath << std::endl;

            if (running) {
                std::this_thread::sleep_for(std::chrono::seconds(2));
            }
        }

        std::cout << "停止监控串口: " << portPath << std::endl;
    }

    // LED控制函数（预留实现）
    void controlLED(int portIndex, bool turnOn) {
        if (portIndex < 0 || portIndex >= static_cast<int>(ledControllers.size())) {
            return;
        }

        const auto& ledController = ledControllers[portIndex];

        // 这里是LED控制的预留实现
        // 实际使用时需要根据具体硬件平台修改
        std::cout << "LED控制 - 端口索引: " << portIndex
                  << ", LED路径: " << ledController.ledPath
                  << ", 状态: " << (turnOn ? "点亮" : "熄灭") << std::endl;

        // 示例：通过sysfs控制LED（需要root权限）
        /*
        std::ofstream ledFile(ledController.ledPath);
        if (ledFile.is_open()) {
            ledFile << (turnOn ? "1" : "0");
            ledFile.close();
        }
        */
    }

    // 启动监控
    void start() {
        if (running) {
            std::cout << "监控已经在运行中" << std::endl;
            return;
        }

        running = true;

        std::cout << "启动串口监控程序..." << std::endl;
        std::cout << "监控的串口列表:" << std::endl;

        // 为每个串口创建监控线程
        for (size_t i = 0; i < serialPorts.size(); ++i) {
            std::cout << "  [" << i << "] " << serialPorts[i] << std::endl;

            monitorThreads.emplace_back([this, i]() {
                monitorSerialPort(serialPorts[i], i);
            });
        }

        std::cout << "所有监控线程已启动" << std::endl;
    }

    // 停止监控
    void stop() {
        if (!running) {
            return;
        }

        std::cout << "正在停止串口监控..." << std::endl;
        running = false;

        // 等待所有线程结束
        for (auto& thread : monitorThreads) {
            if (thread.joinable()) {
                thread.join();
            }
        }

        monitorThreads.clear();
        std::cout << "串口监控已停止" << std::endl;
    }

    // 获取监控状态
    bool isRunning() const {
        return running;
    }

    // 添加串口到监控列表
    void addSerialPort(const std::string& portPath) {
        serialPorts.push_back(portPath);
        std::string ledPath = "/sys/class/leds/led" + std::to_string(serialPorts.size() - 1) + "/brightness";
        ledControllers.emplace_back(ledPath, serialPorts.size() - 1);
    }

    // 显示当前监控的串口
    void showMonitoredPorts() {
        std::cout << "当前监控的串口:" << std::endl;
        for (size_t i = 0; i < serialPorts.size(); ++i) {
            std::cout << "  [" << i << "] " << serialPorts[i]
                      << " -> LED: " << ledControllers[i].ledPath << std::endl;
        }
    }
};

// 全局变量用于信号处理
std::unique_ptr<SerialPortMonitor> g_monitor;

// 信号处理函数
void signalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在退出..." << std::endl;
    if (g_monitor) {
        g_monitor->stop();
    }
    exit(0);
}

int main() {
    std::cout << "=== ARM32 Linux 串口监控与LED控制程序 ===" << std::endl;
    std::cout << "版本: 1.0" << std::endl;
    std::cout << "功能: 监控RS485/RS232串口数据传输并控制LED指示" << std::endl;
    std::cout << "=========================================" << std::endl;

    // 注册信号处理
    signal(SIGINT, signalHandler);   // Ctrl+C
    signal(SIGTERM, signalHandler);  // 终止信号

    // 创建监控器
    g_monitor = std::make_unique<SerialPortMonitor>();

    // 显示要监控的串口
    g_monitor->showMonitoredPorts();

    // 启动监控
    g_monitor->start();

    // 主循环
    std::cout << "\n程序运行中... 按 Ctrl+C 退出" << std::endl;

    while (g_monitor->isRunning()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    return 0;
}