#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <atomic>
#include <chrono>
#include <unistd.h>
#include <sys/stat.h>
#include <signal.h>

class SerialMonitor {
private:
    std::vector<std::string> serialPorts;
    std::vector<std::thread> monitorThreads;
    std::atomic<bool> running;

public:
    SerialMonitor() : running(false) {
        // 要监控的串口列表
        serialPorts = {
            "/dev/ttymxc1",
            "/dev/ttymxc3",
            "/dev/ttymxc6",
            "/dev/ttymxc7"
        };
    }

    ~SerialMonitor() {
        stop();
    }

    // 监控设备文件的访问时间变化
    void monitorPort(const std::string& portPath, int portIndex) {
        struct stat lastStat;
        bool hasLastStat = false;

        while (running) {
            struct stat currentStat;
            if (stat(portPath.c_str(), &currentStat) == 0) {
                if (hasLastStat) {
                    // 检查访问时间或修改时间是否变化
                    if (currentStat.st_atime != lastStat.st_atime ||
                        currentStat.st_mtime != lastStat.st_mtime) {
                        std::cout << "检测到串口 " << portPath << " 有数据交互" << std::endl;
                    }
                }
                lastStat = currentStat;
                hasLastStat = true;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }

    // 方法2：监控 /sys/class/tty/ttyXXX/device/statistics/ 统计信息
    void monitorByStatistics(const std::string& portPath, int portIndex) {
        std::cout << "使用统计信息监控: " << portPath << std::endl;
        
        // 提取设备名（如 ttymxc1）
        std::string deviceName = portPath.substr(portPath.find_last_of('/') + 1);
        std::string statsPath = "/sys/class/tty/" + deviceName + "/device/";
        
        // 检查统计路径是否存在
        struct stat statBuf;
        if (stat(statsPath.c_str(), &statBuf) != 0) {
            std::cout << "统计路径不存在: " << statsPath << std::endl;
            return;
        }
        
        unsigned long lastRxBytes = 0, lastTxBytes = 0;
        bool hasLastStats = false;
        
        while (running) {
            // 尝试读取接收字节数
            std::ifstream rxFile(statsPath + "rx_bytes");
            std::ifstream txFile(statsPath + "tx_bytes");
            
            unsigned long currentRx = 0, currentTx = 0;
            
            if (rxFile.is_open()) {
                rxFile >> currentRx;
            }
            if (txFile.is_open()) {
                txFile >> currentTx;
            }
            
            if (hasLastStats && (currentRx > lastRxBytes || currentTx > lastTxBytes)) {
                std::cout << "检测到串口 " << portPath << " 数据传输 (RX: " 
                          << lastRxBytes << "->" << currentRx << ", TX: " 
                          << lastTxBytes << "->" << currentTx << ")" << std::endl;
                controlLED(portIndex, true);
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
                controlLED(portIndex, false);
            }
            
            lastRxBytes = currentRx;
            lastTxBytes = currentTx;
            hasLastStats = true;
            
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    // 方法3：监控进程的文件描述符使用情况
    void monitorByProcessFD(const std::string& portPath, int portIndex) {
        std::cout << "使用进程文件描述符监控: " << portPath << std::endl;
        
        std::set<pid_t> lastProcesses;
        
        while (running) {
            std::set<pid_t> currentProcesses;
            
            // 使用 lsof 查找使用该串口的进程
            std::string command = "lsof -t " + portPath + " 2>/dev/null";
            FILE* pipe = popen(command.c_str(), "r");
            
            if (pipe) {
                char buffer[256];
                while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
                    pid_t pid = std::atoi(buffer);
                    if (pid > 0) {
                        currentProcesses.insert(pid);
                    }
                }
                pclose(pipe);
            }
            
            // 检查进程列表变化
            if (currentProcesses != lastProcesses) {
                if (!currentProcesses.empty()) {
                    std::cout << "检测到串口 " << portPath << " 被进程使用: ";
                    for (pid_t pid : currentProcesses) {
                        std::cout << pid << " ";
                    }
                    std::cout << std::endl;
                    
                    controlLED(portIndex, true);
                    std::this_thread::sleep_for(std::chrono::milliseconds(500));
                    controlLED(portIndex, false);
                }
                lastProcesses = currentProcesses;
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(2));
        }
    }

    // 方法4：监控设备文件的访问时间（最轻量级）
    void monitorByAccessTime(const std::string& portPath, int portIndex) {
        std::cout << "使用访问时间监控: " << portPath << std::endl;
        
        struct stat lastStat;
        bool hasLastStat = false;
        
        while (running) {
            struct stat currentStat;
            if (stat(portPath.c_str(), &currentStat) == 0) {
                if (hasLastStat) {
                    // 检查访问时间或修改时间是否变化
                    if (currentStat.st_atime != lastStat.st_atime || 
                        currentStat.st_mtime != lastStat.st_mtime) {
                        std::cout << "检测到串口 " << portPath << " 文件访问变化" << std::endl;
                        
                        controlLED(portIndex, true);
                        std::this_thread::sleep_for(std::chrono::milliseconds(500));
                        controlLED(portIndex, false);
                    }
                }
                lastStat = currentStat;
                hasLastStat = true;
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }

    // LED控制函数（预留实现）
    void controlLED(int portIndex, bool turnOn) {
        std::cout << "LED控制 - 端口索引: " << portIndex 
                  << ", 状态: " << (turnOn ? "点亮" : "熄灭") << std::endl;
    }

    // 启动监控
    void start(int method = 1) {
        if (running) {
            std::cout << "监控已经在运行中" << std::endl;
            return;
        }
        
        running = true;
        
        std::cout << "启动完全非侵入式串口监控..." << std::endl;
        std::cout << "监控方法: ";
        
        switch (method) {
            case 1: std::cout << "中断计数监控" << std::endl; break;
            case 2: std::cout << "统计信息监控" << std::endl; break;
            case 3: std::cout << "进程文件描述符监控" << std::endl; break;
            case 4: std::cout << "文件访问时间监控" << std::endl; break;
            default: std::cout << "未知方法，使用默认方法" << std::endl; method = 4; break;
        }
        
        // 为每个串口创建监控线程
        for (size_t i = 0; i < serialPorts.size(); ++i) {
            std::cout << "  [" << i << "] " << serialPorts[i] << std::endl;
            
            monitorThreads.emplace_back([this, i, method]() {
                switch (method) {
                    case 1: monitorByInterrupts(serialPorts[i], i); break;
                    case 2: monitorByStatistics(serialPorts[i], i); break;
                    case 3: monitorByProcessFD(serialPorts[i], i); break;
                    case 4: monitorByAccessTime(serialPorts[i], i); break;
                }
            });
        }
        
        std::cout << "所有监控线程已启动" << std::endl;
    }

    // 停止监控
    void stop() {
        if (!running) return;
        
        std::cout << "正在停止串口监控..." << std::endl;
        running = false;
        
        for (auto& thread : monitorThreads) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        
        monitorThreads.clear();
        std::cout << "串口监控已停止" << std::endl;
    }

    bool isRunning() const { return running; }
};

// 全局变量用于信号处理
std::unique_ptr<NonInvasiveSerialMonitor> g_monitor;

// 信号处理函数
void signalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在退出..." << std::endl;
    if (g_monitor) {
        g_monitor->stop();
    }
    exit(0);
}

void printUsage(const char* programName) {
    std::cout << "用法: " << programName << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -m, --method <方法>  监控方法:" << std::endl;
    std::cout << "                       1 = 中断计数监控（推荐）" << std::endl;
    std::cout << "                       2 = 统计信息监控" << std::endl;
    std::cout << "                       3 = 进程文件描述符监控" << std::endl;
    std::cout << "                       4 = 文件访问时间监控（默认）" << std::endl;
    std::cout << "  -h, --help           显示此帮助信息" << std::endl;
}

int main(int argc, char* argv[]) {
    std::cout << "=== 完全非侵入式串口监控程序 ===" << std::endl;
    std::cout << "版本: 2.0" << std::endl;
    std::cout << "特点: 完全不影响其他程序的串口读写" << std::endl;
    std::cout << "=====================================" << std::endl;

    int method = 4;  // 默认使用文件访问时间监控

    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            printUsage(argv[0]);
            return 0;
        } else if (arg == "-m" || arg == "--method") {
            if (i + 1 < argc) {
                method = std::atoi(argv[i + 1]);
                if (method < 1 || method > 4) {
                    std::cout << "错误: 无效的监控方法 " << method << std::endl;
                    printUsage(argv[0]);
                    return 1;
                }
                i++; // 跳过方法参数
            } else {
                std::cout << "错误: -m/--method 需要一个参数" << std::endl;
                printUsage(argv[0]);
                return 1;
            }
        }
    }

    // 注册信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    // 创建监控器
    g_monitor = std::make_unique<NonInvasiveSerialMonitor>();

    // 启动监控
    g_monitor->start(method);

    // 主循环
    std::cout << "\n程序运行中... 按 Ctrl+C 退出" << std::endl;
    std::cout << "此程序完全不会影响其他程序的串口读写操作" << std::endl;

    while (g_monitor->isRunning()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    return 0;
}
