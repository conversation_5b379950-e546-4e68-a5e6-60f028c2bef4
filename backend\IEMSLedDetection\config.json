{"program_info": {"name": "IEMSLedDetection", "version": "1.0", "description": "ARM32 Linux串口监控与LED控制程序"}, "serial_ports": [{"device": "/dev/ttymxc1", "index": 0, "enabled": true, "description": "RS485端口1"}, {"device": "/dev/ttymxc2", "index": 1, "enabled": true, "description": "RS485端口2"}, {"device": "/dev/ttymxc3", "index": 2, "enabled": true, "description": "RS485端口3"}, {"device": "/dev/ttyUSB0", "index": 3, "enabled": true, "description": "USB转串口"}], "led_controllers": [{"port_index": 0, "led_path": "/sys/class/leds/led0/brightness", "enabled": true, "description": "端口0对应LED"}, {"port_index": 1, "led_path": "/sys/class/leds/led1/brightness", "enabled": true, "description": "端口1对应LED"}, {"port_index": 2, "led_path": "/sys/class/leds/led2/brightness", "enabled": true, "description": "端口2对应LED"}, {"port_index": 3, "led_path": "/sys/class/leds/led3/brightness", "enabled": true, "description": "端口3对应LED"}], "monitor_settings": {"check_interval_ms": 1000, "reconnect_delay_s": 5, "led_on_duration_ms": 500, "buffer_size": 1024}, "logging": {"enabled": true, "level": "INFO", "log_data_activity": true, "log_led_control": true}}