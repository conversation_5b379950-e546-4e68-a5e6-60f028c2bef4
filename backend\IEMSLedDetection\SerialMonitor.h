#pragma once

#include <vector>
#include <string>
#include <thread>
#include <atomic>

class SerialMonitor {
private:
    std::vector<std::string> serialPorts;
    std::vector<std::thread> monitorThreads;
    std::atomic<bool> running;

    // 使用 inotify 实时监控
    void monitorPort(const std::string& portPath, int portIndex);

public:
    SerialMonitor();
    ~SerialMonitor();

    // 启动监控
    void start();

    // 停止监控
    void stop();

    bool isRunning() const;
};
