#!/bin/bash

# 使用 strace 监控串口系统调用的脚本
# 这种方法完全不影响串口数据传输

echo "=== 系统调用监控串口活动 ==="

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "请以root权限运行此脚本: sudo $0"
    exit 1
fi

# 要监控的串口设备
SERIAL_PORTS=("/dev/ttymxc1" "/dev/ttymxc3" "/dev/ttymxc6" "/dev/ttymxc7")

echo "监控的串口设备:"
for port in "${SERIAL_PORTS[@]}"; do
    echo "  - $port"
done

echo ""
echo "开始监控系统调用..."
echo "当有程序读写串口时，会显示相关信息"
echo "按 Ctrl+C 停止监控"
echo "----------------------------------------"

# 使用 strace 监控所有进程对串口设备的系统调用
# -e trace=read,write,open,openat,close 只监控相关系统调用
# -f 跟踪子进程
# -s 0 不显示字符串内容，只显示长度
# 2>&1 重定向错误输出

strace -e trace=read,write,open,openat,close -f -s 0 -p 1 2>&1 | \
while IFS= read -r line; do
    # 检查是否包含我们关心的串口设备
    for port in "${SERIAL_PORTS[@]}"; do
        if [[ "$line" == *"$port"* ]]; then
            timestamp=$(date '+%H:%M:%S')
            echo "[$timestamp] 检测到串口活动: $line"
            
            # 这里可以添加LED控制逻辑
            # echo "1" > /sys/class/leds/led0/brightness
            # sleep 0.5
            # echo "0" > /sys/class/leds/led0/brightness
            
            break
        fi
    done
done
