﻿# CMakeList.txt : CMake project for Gateway, include source and define
# project specific logic here.
#
cmake_minimum_required (VERSION 3.10)

# do not specify the project language
project (Gateway VERSION 1.0)

# common variable
if (NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE Release)
endif ()
message(STATUS "build type is " ${CMAKE_BUILD_TYPE})

if (WIN32 AND ${CMAKE_BUILD_TYPE} STREQUAL "Release")
    message(STATUS "message win32 and release")
endif()

if (NOT PLATFORM)
  set(PLATFORM Win32)
endif ()

set(PROJ_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
message(STATUS "project root dir is ${PROJ_ROOT_DIR}")
set(OPEN_SOURCE_DIR ${PROJ_ROOT_DIR}/OpenSource)
set(UTILS_DIR ${PROJ_ROOT_DIR}/Utils)
set(OUTPUT_DIR ${PROJ_ROOT_DIR}/bin/${PLATFORM}/${CMAKE_BUILD_TYPE})
set(BUILD_DIR  ${PROJ_ROOT_DIR}/build/${PLATFORM}/${CMAKE_BUILD_TYPE})
set(SYSTEMSETTING_DIR ${PROJ_ROOT_DIR}/SystemSetting)

set(CMAKE_C_STANDARD 99)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)

# close optimize
#add_compile_options(-O2)

if (DETECT_MEM_LEAK AND UNIX)
    add_compile_options(-fsanitize=address -fno-omit-frame-pointer)
    link_libraries(-fsanitize=address)
endif ()

# binary lib
set(OPENSSL_DIR ${PROJ_ROOT_DIR}/Library/openssl-1.1.1n)
set(LIBCURL_DIR ${PROJ_ROOT_DIR}/Library/libcurl-7.82.0)
set(LIBPQXX_DIR ${PROJ_ROOT_DIR}/Library/libpqxx-6.4)
set(LIBBSON_DIR ${PROJ_ROOT_DIR}/Library/libbson)
set(MQTT_DIR ${PROJ_ROOT_DIR}/Library/paho.mqtt.c-1.3.8)
set(LIBPLCTAG_DIR ${PROJ_ROOT_DIR}/Library/libplctag-2.3.6)
set(IEC60870_DIR ${PROJ_ROOT_DIR}/Library/lib60870-2.3.1)
set(MYSQL_DIR ${PROJ_ROOT_DIR}/Library/mysql-connector-c-6.1.11)
set(DAHUA_SDK_DIR ${PROJ_ROOT_DIR}/Library/dahua-device-network-sdk-V3.055.0000000.0.R.210524)
set(HKOPENAPI_DIR ${PROJ_ROOT_DIR}/Library/hkopenapi)
set(SQLITE_DIR ${PROJ_ROOT_DIR}/Library/sqlite-autoconf-3380500)
set(UNIXODBC_DIR ${PROJ_ROOT_DIR}/Library/unixODBC-2.3.11)
set(ZLIB_DIR ${PROJ_ROOT_DIR}/Library/zlib-1.2.13)
set(HCNETSDK_DIR ${PROJ_ROOT_DIR}/Library/hcNetsdk)
set(COAP_DIR ${PROJ_ROOT_DIR}/Library/libcoap-4.3.5)

# source lib
set(HTTP_DIR ${OPEN_SOURCE_DIR}/httplib-0.11.2)

set(FORMATTING_DIR ${OPEN_SOURCE_DIR}/fmt-8.1.1)
set(FORMATTING_SRC
    ${FORMATTING_DIR}/fmt.cc
    ${FORMATTING_DIR}/format.cc
    ${FORMATTING_DIR}/os.cc
)

set(FASTJSON_DIR ${OPEN_SOURCE_DIR}/yyjson-0.5.1)
set(FASTJSON_SRC
    ${FASTJSON_DIR}/yyjson.c
)

set(BOOST_HPP_DIR ${OPEN_SOURCE_DIR})

set(LOGGING_DIR ${OPEN_SOURCE_DIR}/spdlog-1.9.2)

set(MQTTC_DIR ${OPEN_SOURCE_DIR}/paho.mqtt.c-1.3.8)
set(MQTTC_SRC
    ${MQTTC_DIR}/MQTTTime.c
    ${MQTTC_DIR}/MQTTProtocolClient.c
    ${MQTTC_DIR}/Clients.c
    ${MQTTC_DIR}/utf-8.c
    ${MQTTC_DIR}/MQTTPacket.c
    ${MQTTC_DIR}/MQTTPacketOut.c
    ${MQTTC_DIR}/Messages.c
    ${MQTTC_DIR}/Tree.c
    ${MQTTC_DIR}/Socket.c
    ${MQTTC_DIR}/Log.c
    ${MQTTC_DIR}/MQTTPersistence.c
    ${MQTTC_DIR}/Thread.c
    ${MQTTC_DIR}/MQTTProtocolOut.c
    ${MQTTC_DIR}/MQTTPersistenceDefault.c
    ${MQTTC_DIR}/SocketBuffer.c
    ${MQTTC_DIR}/LinkedList.c
    ${MQTTC_DIR}/MQTTProperties.c
    ${MQTTC_DIR}/MQTTReasonCodes.c
    ${MQTTC_DIR}/Base64.c
    ${MQTTC_DIR}/SHA1.c
    ${MQTTC_DIR}/WebSocket.c
    ${MQTTC_DIR}/Base64.c
    ${MQTTC_DIR}/SHA1.c
    ${MQTTC_DIR}/WebSocket.c
    ${MQTTC_DIR}/MQTTAsync.c
    ${MQTTC_DIR}/MQTTAsyncUtils.c
    ${MQTTC_DIR}/SSLSocket.c
)

set(JSON_DIR ${OPEN_SOURCE_DIR}/json-3.9.1)
set(JSON_SRC ${JSON_DIR}/nlohmann/json.hpp)

set(JSONCPP_DIR ${OPEN_SOURCE_DIR}/jsoncpp-1.9.4)
set(JSONCPP_SRC
    ${JSONCPP_DIR}/json_reader.cpp
    ${JSONCPP_DIR}/json_value.cpp
    ${JSONCPP_DIR}/json_writer.cpp
)

set(LIBMODBUS_DIR ${OPEN_SOURCE_DIR}/libmodbus-3.1.10)
set(LIBMODBUS_SRC
    ${LIBMODBUS_DIR}/modbus.c
    ${LIBMODBUS_DIR}/modbus-data.c
    ${LIBMODBUS_DIR}/modbus-rtu.c
    ${LIBMODBUS_DIR}/modbus-tcp.c
)

set(ZIP_DIR ${OPEN_SOURCE_DIR}/zip-0.2.2)
set(ZIP_SRC ${ZIP_DIR}/zip.c)

set(LOG4Z_DIR ${OPEN_SOURCE_DIR}/log4z)
set(LOG4Z_SRC
	${LOG4Z_DIR}/Log4z.cpp
)

set(MONGOOSE_DIR ${OPEN_SOURCE_DIR}/mongoose-7.7)
set(MONGOOSE_SRC
	${MONGOOSE_DIR}/mongoose.c
)

set(OPEN62541_DIR ${OPEN_SOURCE_DIR}/open62541)
set(OPEN62541_SRC
	${OPEN62541_DIR}/open62541.c
)

set(LUA_DIR ${OPEN_SOURCE_DIR}/lua)
set(LUA_SRC
	${LUA_DIR}/lapi.c
	${LUA_DIR}/lauxlib.c
    ${LUA_DIR}/lbaselib.c
	${LUA_DIR}/lauxlib.c
    ${LUA_DIR}/lbitlib.c
	${LUA_DIR}/lcode.c
    ${LUA_DIR}/lcorolib.c
	${LUA_DIR}/lctype.c
    ${LUA_DIR}/ldblib.c
	${LUA_DIR}/ldebug.c
    ${LUA_DIR}/ldo.c
	${LUA_DIR}/ldump.c
    ${LUA_DIR}/lfunc.c
	${LUA_DIR}/lgc.c
    ${LUA_DIR}/linit.c
	${LUA_DIR}/liolib.c
    ${LUA_DIR}/llex.c
	${LUA_DIR}/lmathlib.c
    ${LUA_DIR}/lmem.c
	${LUA_DIR}/loadlib.c
    ${LUA_DIR}/lobject.c
	${LUA_DIR}/lopcodes.c
    ${LUA_DIR}/loslib.c
	${LUA_DIR}/lparser.c
    ${LUA_DIR}/lstate.c
	${LUA_DIR}/lstring.c
    ${LUA_DIR}/lstrlib.c
    ${LUA_DIR}/ltable.c
    ${LUA_DIR}/ltablib.c
    ${LUA_DIR}/ltm.c
    #${LUA_DIR}/lua.c
    #${LUA_DIR}/luac.c
    ${LUA_DIR}/lundump.c
    ${LUA_DIR}/lutf8lib.c
    ${LUA_DIR}/lvm.c
    ${LUA_DIR}/lzio.c
)
#add_subdirectory(${LUA_DIR}) #TO-DO: lua lib is special. try to fix this

set(TINYXML_DIR ${OPEN_SOURCE_DIR}/tinyxml2-9.0.0)
set(TINYXML_SRC
	${TINYXML_DIR}/tinyxml2.cpp
)

set(JS_DIR ${OPEN_SOURCE_DIR}/quickjs-2021-03-27)
set(JS_SRC
	${JS_DIR}/cutils.c
    ${JS_DIR}/libbf.c
    ${JS_DIR}/libregexp.c
    ${JS_DIR}/libunicode.c
    ${JS_DIR}/quickjs.c
    ${JS_DIR}/quickjs-libc.c
)

set(OPCUA_SERVER_DIR ${PROJ_ROOT_DIR}/OPCUAServer)
set(OPCUA_SERVER_SRC ${OPCUA_SERVER_DIR}/OpcUaServer.cpp)

set(COMMUNICATE_DIR ${PROJ_ROOT_DIR}/Communicate)

set(IEMS_SETTING_DIR ${PROJ_ROOT_DIR}/IEMSSetting)

add_subdirectory(IEMSSetting)
add_subdirectory(Communicate)
add_subdirectory(SystemSetting)
add_subdirectory(DriverPlugin)
add_subdirectory(OPCUAServer)
add_subdirectory(DriverX)
if(NOT PLATFORM STREQUAL Win32)
    add_subdirectory(Manager)
endif()

if(PLATFORM STREQUAL "Arm32")
    add_subdirectory(IEMSLedDetection)
endif()

#if(NOT PLATFORM STREQUAL Win32)
#    add_subdirectory(Updater)
#endif()
#add_subdirectory(DriverAdapter)
#add_subdirectory(ModbusTcpServer)
#add_subdirectory(Test)
