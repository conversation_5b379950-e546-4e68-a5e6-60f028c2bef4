# IEMSLedDetection - 串口监控与LED控制程序

## 功能描述

这是一个专为ARM32 Linux环境设计的串口监控程序，用于监控RS485/RS232串口的数据传输状态，并通过板载LED提供可视化指示。

## 主要功能

1. **串口监控**: 实时监控多个串口的数据传输活动
2. **LED控制**: 当检测到串口数据传输时，点亮对应的LED指示灯（预留功能）
3. **多线程处理**: 每个串口使用独立线程进行监控
4. **自动重连**: 串口断开后自动尝试重新连接
5. **信号处理**: 支持优雅退出（Ctrl+C）
6. **多种监控模式**: 支持侵入式和非侵入式监控

## 监控模式

### 🟢 非侵入式监控（推荐，默认）
- **不会影响其他程序**使用串口
- 只检测串口是否有数据活动，不读取实际数据
- 适用于已有程序在使用串口的情况

### 🟡 进程监控
- 通过监控使用串口的进程来检测活动
- 使用`lsof`命令检查进程状态
- 对其他程序影响最小

### 🟡 文件系统监控
- 监控设备文件的访问时间变化
- 基于文件系统的访问统计
- 轻量级监控方式

### 🔴 侵入式监控
- **可能影响其他程序**使用串口
- 直接读取串口数据
- 仅在确认没有其他程序使用串口时使用

## 监控的串口列表

程序默认监控以下串口（在代码中写死）：
- `/dev/ttymxc1`
- `/dev/ttymxc2` 
- `/dev/ttymxc3`
- `/dev/ttyUSB0`

## 编译方法

### 使用项目构建脚本
```bash
# ARM32平台编译
cd /path/to/backend
./build_arm32.sh
```

### 手动编译
```bash
cd backend/IEMSLedDetection
mkdir build && cd build
cmake ..
make
```

### 交叉编译（ARM32）
```bash
cd backend/IEMSLedDetection
mkdir build && cd build
cmake -DCMAKE_TOOLCHAIN_FILE=../../toolchain_arm32.cmake ..
make
```

## 运行方法

```bash
# 默认使用非侵入式监控（推荐）
sudo ./IEMSLedDetection

# 指定监控模式
sudo ./IEMSLedDetection -m 0  # 非侵入式（默认）
sudo ./IEMSLedDetection -m 1  # 进程监控
sudo ./IEMSLedDetection -m 2  # 文件系统监控
sudo ./IEMSLedDetection -m 3  # 侵入式（谨慎使用）

# 显示帮助信息
./IEMSLedDetection -h
```

## 命令行参数

- `-m, --mode <模式>`: 指定监控模式（0-3）
- `-h, --help`: 显示帮助信息

## 程序输出示例

### 非侵入式监控模式
```
=== ARM32 Linux 串口监控与LED控制程序 ===
版本: 1.0
功能: 监控RS485/RS232串口数据传输并控制LED指示
=========================================
监控模式: 非侵入式（推荐）- 不会影响其他程序
当前监控的串口:
  [0] /dev/ttymxc1 -> LED: /sys/class/leds/led0/brightness
  [1] /dev/ttymxc2 -> LED: /sys/class/leds/led1/brightness
  [2] /dev/ttymxc3 -> LED: /sys/class/leds/led2/brightness
  [3] /dev/ttyUSB0 -> LED: /sys/class/leds/led3/brightness

启动串口监控程序...
所有监控线程已启动

程序运行中... 按 Ctrl+C 退出
开始监控串口: /dev/ttymxc1 (索引: 0)
使用非侵入式监控: /dev/ttymxc1
检测到串口 /dev/ttymxc1 有数据活动（非侵入式）
LED控制 - 端口索引: 0, LED路径: /sys/class/leds/led0/brightness, 状态: 点亮
LED控制 - 端口索引: 0, LED路径: /sys/class/leds/led0/brightness, 状态: 熄灭
```

### 进程监控模式
```
监控模式: 进程监控 - 监控使用串口的进程
开始监控串口: /dev/ttymxc1 (索引: 0)
使用进程监控: /dev/ttymxc1
检测到串口 /dev/ttymxc1 使用状态变化
```

## Linux命令行监控串口的方法

除了本程序外，还可以使用以下Linux命令监控串口：

### 1. 使用cat命令
```bash
# 直接读取串口数据
cat /dev/ttymxc1

# 或者使用超时
timeout 10 cat /dev/ttymxc1
```

### 2. 使用minicom
```bash
# 安装minicom
sudo apt-get install minicom

# 配置并监控串口
sudo minicom -D /dev/ttymxc1
```

### 3. 使用socat
```bash
# 安装socat
sudo apt-get install socat

# 监控串口数据
socat -u /dev/ttymxc1,raw,echo=0 STDOUT
```

### 4. 使用strace监控进程
```bash
# 监控某个进程的串口访问
sudo strace -e trace=read,write -p <pid>
```

### 5. 使用lsof查看串口使用情况
```bash
# 查看哪些进程在使用串口
sudo lsof /dev/ttymxc1
```

## LED控制说明

当前LED控制功能为预留实现，实际使用时需要根据具体硬件平台修改LED路径。

### 常见LED控制路径
- 通用GPIO LED: `/sys/class/leds/ledX/brightness`
- 用户自定义LED: `/sys/class/leds/user-ledX/brightness`
- 板载状态LED: `/sys/class/leds/status-ledX/brightness`

### 启用LED控制
取消注释main.cpp中controlLED函数的实际实现部分：
```cpp
std::ofstream ledFile(ledController.ledPath);
if (ledFile.is_open()) {
    ledFile << (turnOn ? "1" : "0");
    ledFile.close();
}
```

## ⚠️ 重要说明：对其他程序的影响

### 🟢 推荐使用非侵入式监控
**默认的非侵入式监控模式不会影响其他程序使用串口**，因为：
- 只检测串口是否有数据可读，不实际读取数据
- 其他程序仍可正常读取串口数据
- 不会消费串口缓冲区中的数据

### 🔴 避免使用侵入式监控
**侵入式监控模式会影响其他程序**，因为：
- 会读取并消费串口缓冲区中的数据
- 其他程序可能无法接收到完整数据
- 可能导致数据丢失或通信错误

### 📋 使用建议
1. **有其他程序使用串口时**: 使用模式0（非侵入式）
2. **独占使用串口时**: 可以使用模式3（侵入式）获得更准确的监控
3. **不确定时**: 始终使用模式0（非侵入式），这是最安全的选择

## 注意事项

1. **权限要求**: 程序需要root权限来访问串口设备和LED控制文件
2. **串口配置**: 程序使用原始模式打开串口，适用于大多数RS485/RS232通信
3. **线程安全**: 每个串口使用独立线程，避免相互干扰
4. **资源管理**: 程序会自动清理资源，支持优雅退出
5. **硬件兼容**: 针对ARM32平台优化，支持常见的嵌入式Linux系统
6. **监控模式选择**: 根据实际使用情况选择合适的监控模式

## 扩展功能

程序设计时预留了以下扩展接口：
- 动态添加/删除监控串口
- 自定义LED控制逻辑
- 数据包分析和过滤
- 网络状态上报
- 配置文件支持

## 故障排除

### 串口无法打开
- 检查串口设备是否存在: `ls -l /dev/tty*`
- 检查权限: `sudo chmod 666 /dev/ttymxc1`
- 检查是否被其他程序占用: `sudo lsof /dev/ttymxc1`

### LED无法控制
- 检查LED设备路径: `ls -l /sys/class/leds/`
- 检查权限: `sudo chmod 666 /sys/class/leds/led0/brightness`
- 确认LED驱动已加载

### 编译错误
- 确保安装了必要的开发工具: `sudo apt-get install build-essential cmake`
- 检查交叉编译工具链配置
- 确认CMake版本兼容性
