# Makefile for IEMSLedDetection
# 备用编译方法，当CMake不可用时使用

# 编译器设置
CXX = g++
ARM_CXX = arm-linux-gnueabihf-g++

# 编译选项
CXXFLAGS = -std=c++14 -Wall -Wextra -O2
DEBUG_FLAGS = -g -DDEBUG
ARM_FLAGS = -march=armv7-a

# 链接选项
LDFLAGS = -pthread

# 源文件和目标文件
SRCDIR = .
MAIN_SOURCES = main.cpp
TEST_SOURCES = simple_test.cpp
NONINVASIVE_SOURCES = non_invasive_monitor.cpp
INOTIFY_SOURCES = inotify_monitor.cpp

MAIN_OBJECTS = $(MAIN_SOURCES:.cpp=.o)
TEST_OBJECTS = $(TEST_SOURCES:.cpp=.o)
NONINVASIVE_OBJECTS = $(NONINVASIVE_SOURCES:.cpp=.o)
INOTIFY_OBJECTS = $(INOTIFY_SOURCES:.cpp=.o)

TARGET = IEMSLedDetection
TEST_TARGET = simple_test
NONINVASIVE_TARGET = non_invasive_monitor
INOTIFY_TARGET = inotify_monitor

# 默认目标
all: $(TARGET) $(TEST_TARGET)

# 编译主程序
$(TARGET): $(MAIN_OBJECTS)
	$(CXX) $(MAIN_OBJECTS) -o $(TARGET) $(LDFLAGS)

# 编译测试程序
$(TEST_TARGET): $(TEST_OBJECTS)
	$(CXX) $(TEST_OBJECTS) -o $(TEST_TARGET) $(LDFLAGS)

# 编译源文件
%.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

# ARM交叉编译
arm: CXX = $(ARM_CXX)
arm: CXXFLAGS += $(ARM_FLAGS)
arm: $(TARGET)

# Debug版本
debug: CXXFLAGS += $(DEBUG_FLAGS)
debug: $(TARGET)

# ARM Debug版本
arm-debug: CXX = $(ARM_CXX)
arm-debug: CXXFLAGS += $(ARM_FLAGS) $(DEBUG_FLAGS)
arm-debug: $(TARGET)

# 清理
clean:
	rm -f $(MAIN_OBJECTS) $(TEST_OBJECTS) $(TARGET) $(TEST_TARGET)

# 安装
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/
	chmod +x /usr/local/bin/$(TARGET)

# 卸载
uninstall:
	rm -f /usr/local/bin/$(TARGET)

# 测试
test: $(TARGET)
	sudo ./$(TARGET)

# 测试简单监控
test-simple: $(TEST_TARGET)
	sudo ./$(TEST_TARGET)

# 显示帮助
help:
	@echo "可用的make目标:"
	@echo "  all        - 编译程序和测试程序 (默认)"
	@echo "  arm        - ARM交叉编译"
	@echo "  debug      - 编译Debug版本"
	@echo "  arm-debug  - ARM交叉编译Debug版本"
	@echo "  clean      - 清理编译文件"
	@echo "  install    - 安装到系统"
	@echo "  uninstall  - 从系统卸载"
	@echo "  test       - 运行主程序"
	@echo "  test-simple- 运行简单测试程序"
	@echo "  help       - 显示此帮助"

# 伪目标
.PHONY: all arm debug arm-debug clean install uninstall test test-simple help
