# Makefile for IEMSLedDetection
# 备用编译方法，当CMake不可用时使用

# 编译器设置
CXX = g++
ARM_CXX = arm-linux-gnueabihf-g++

# 编译选项
CXXFLAGS = -std=c++14 -Wall -Wextra -O2
DEBUG_FLAGS = -g -DDEBUG
ARM_FLAGS = -march=armv7-a

# 链接选项
LDFLAGS = -pthread

# 源文件和目标文件
SRCDIR = .
SOURCES = $(wildcard $(SRCDIR)/*.cpp)
OBJECTS = $(SOURCES:.cpp=.o)
TARGET = IEMSLedDetection

# 默认目标
all: $(TARGET)

# 编译目标
$(TARGET): $(OBJECTS)
	$(CXX) $(OBJECTS) -o $(TARGET) $(LDFLAGS)

# 编译源文件
%.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

# ARM交叉编译
arm: CXX = $(ARM_CXX)
arm: CXXFLAGS += $(ARM_FLAGS)
arm: $(TARGET)

# Debug版本
debug: CXXFLAGS += $(DEBUG_FLAGS)
debug: $(TARGET)

# ARM Debug版本
arm-debug: CXX = $(ARM_CXX)
arm-debug: CXXFLAGS += $(ARM_FLAGS) $(DEBUG_FLAGS)
arm-debug: $(TARGET)

# 清理
clean:
	rm -f $(OBJECTS) $(TARGET)

# 安装
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/
	chmod +x /usr/local/bin/$(TARGET)

# 卸载
uninstall:
	rm -f /usr/local/bin/$(TARGET)

# 测试
test: $(TARGET)
	sudo ./$(TARGET)

# 显示帮助
help:
	@echo "可用的make目标:"
	@echo "  all        - 编译程序 (默认)"
	@echo "  arm        - ARM交叉编译"
	@echo "  debug      - 编译Debug版本"
	@echo "  arm-debug  - ARM交叉编译Debug版本"
	@echo "  clean      - 清理编译文件"
	@echo "  install    - 安装到系统"
	@echo "  uninstall  - 从系统卸载"
	@echo "  test       - 运行程序"
	@echo "  help       - 显示此帮助"

# 伪目标
.PHONY: all arm debug arm-debug clean install uninstall test help
