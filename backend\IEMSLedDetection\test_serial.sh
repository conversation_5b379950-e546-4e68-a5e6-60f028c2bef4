#!/bin/bash

# 串口监控测试脚本
# 用于测试IEMSLedDetection程序的功能

echo "=== 串口监控程序测试脚本 ==="

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "请以root权限运行此脚本: sudo $0"
    exit 1
fi

# 检查串口设备
echo "1. 检查串口设备..."
SERIAL_PORTS=("/dev/ttymxc1" "/dev/ttymxc2" "/dev/ttymxc3" "/dev/ttyUSB0")

for port in "${SERIAL_PORTS[@]}"; do
    if [ -e "$port" ]; then
        echo "  ✓ $port 存在"
        ls -l "$port"
    else
        echo "  ✗ $port 不存在"
    fi
done

# 检查LED设备
echo ""
echo "2. 检查LED设备..."
LED_PATHS=("/sys/class/leds/led0/brightness" "/sys/class/leds/led1/brightness" 
           "/sys/class/leds/led2/brightness" "/sys/class/leds/led3/brightness")

for led in "${LED_PATHS[@]}"; do
    if [ -e "$led" ]; then
        echo "  ✓ $led 存在"
        ls -l "$led"
    else
        echo "  ✗ $led 不存在"
    fi
done

# 显示所有可用的LED
echo ""
echo "3. 系统中所有可用的LED:"
if [ -d "/sys/class/leds" ]; then
    ls -la /sys/class/leds/
else
    echo "  /sys/class/leds 目录不存在"
fi

# 显示所有串口设备
echo ""
echo "4. 系统中所有串口设备:"
ls -la /dev/tty* | grep -E "(ttyS|ttyUSB|ttymxc|ttyAMA)"

# 检查程序是否存在
echo ""
echo "5. 检查程序文件..."
PROGRAM_PATH="./IEMSLedDetection"
if [ -f "$PROGRAM_PATH" ]; then
    echo "  ✓ 程序文件存在: $PROGRAM_PATH"
    ls -l "$PROGRAM_PATH"
else
    echo "  ✗ 程序文件不存在: $PROGRAM_PATH"
    echo "  请先编译程序"
fi

# 创建虚拟串口进行测试（可选）
echo ""
echo "6. 创建虚拟串口对进行测试..."
if command -v socat &> /dev/null; then
    echo "  使用socat创建虚拟串口对..."
    echo "  运行命令: socat -d -d pty,raw,echo=0 pty,raw,echo=0"
    echo "  这将创建两个虚拟串口，可以用于测试"
else
    echo "  socat未安装，无法创建虚拟串口"
    echo "  安装命令: apt-get install socat"
fi

# 提供测试数据发送的示例
echo ""
echo "7. 测试数据发送示例:"
echo "  # 向串口发送测试数据"
echo "  echo 'Hello World' > /dev/ttymxc1"
echo "  # 或者使用循环发送"
echo "  while true; do echo 'test data' > /dev/ttymxc1; sleep 2; done"

echo ""
echo "8. 运行程序:"
echo "  sudo ./IEMSLedDetection"

echo ""
echo "=== 测试脚本完成 ==="
