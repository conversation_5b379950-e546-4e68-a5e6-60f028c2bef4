#!/bin/bash

echo "=== 串口监控调试脚本 ==="

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "请以root权限运行此脚本: sudo $0"
    exit 1
fi

echo "1. 检查串口设备是否存在..."
SERIAL_PORTS=("/dev/ttymxc1" "/dev/ttymxc3" "/dev/ttymxc6" "/dev/ttymxc7")

for port in "${SERIAL_PORTS[@]}"; do
    if [ -e "$port" ]; then
        echo "  ✓ $port 存在"
        ls -l "$port"
        
        # 检查权限
        if [ -r "$port" ]; then
            echo "    ✓ 可读"
        else
            echo "    ✗ 不可读"
        fi
        
        if [ -w "$port" ]; then
            echo "    ✓ 可写"
        else
            echo "    ✗ 不可写"
        fi
    else
        echo "  ✗ $port 不存在"
    fi
    echo ""
done

echo "2. 检查所有可用的串口设备..."
echo "系统中所有tty设备:"
ls -la /dev/tty* | grep -E "(ttymxc|ttyS|ttyUSB|ttyAMA)" | head -10

echo ""
echo "3. 测试向串口写入数据..."
TEST_PORT="/dev/ttymxc1"
if [ -e "$TEST_PORT" ]; then
    echo "向 $TEST_PORT 写入测试数据..."
    echo "test data $(date)" > "$TEST_PORT" 2>&1
    if [ $? -eq 0 ]; then
        echo "  ✓ 写入成功"
    else
        echo "  ✗ 写入失败"
    fi
else
    echo "  ✗ $TEST_PORT 不存在"
fi

echo ""
echo "4. 检查是否有其他程序在使用串口..."
for port in "${SERIAL_PORTS[@]}"; do
    if [ -e "$port" ]; then
        echo "检查 $port 的使用情况:"
        lsof "$port" 2>/dev/null || echo "  没有程序在使用此串口"
    fi
done

echo ""
echo "5. 测试直接读取串口（5秒超时）..."
if [ -e "$TEST_PORT" ]; then
    echo "尝试从 $TEST_PORT 读取数据（请在另一个终端发送数据）..."
    timeout 5 cat "$TEST_PORT" || echo "  5秒内没有接收到数据"
fi

echo ""
echo "6. 检查串口配置..."
if [ -e "$TEST_PORT" ]; then
    echo "串口 $TEST_PORT 的配置:"
    stty -F "$TEST_PORT" -a 2>/dev/null || echo "  无法获取串口配置"
fi

echo ""
echo "=== 调试完成 ==="
echo "如果串口存在但无法监控到数据，可能的原因："
echo "1. 串口被其他程序占用"
echo "2. 串口配置不正确"
echo "3. 数据发送方式有问题"
echo "4. 程序的监控逻辑有问题"
