#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <atomic>
#include <chrono>
#include <unistd.h>
#include <fcntl.h>
#include <sys/select.h>
#include <signal.h>

class SimpleSerialMonitor {
private:
    std::vector<std::string> serialPorts;
    std::vector<std::thread> monitorThreads;
    std::atomic<bool> running;

public:
    SimpleSerialMonitor() : running(false) {
        serialPorts = {
            "/dev/ttymxc1",
            "/dev/ttymxc3", 
            "/dev/ttymxc6",
            "/dev/ttymxc7"
        };
    }

    ~SimpleSerialMonitor() {
        stop();
    }

    // 使用 select() 检测数据可读性（不读取数据）
    void monitorPort(const std::string& portPath, int portIndex) {
        while (running) {
            int fd = open(portPath.c_str(), O_RDONLY | O_NONBLOCK);
            if (fd < 0) {
                std::this_thread::sleep_for(std::chrono::seconds(2));
                continue;
            }

            std::cout << "开始监控: " << portPath << std::endl;

            while (running) {
                fd_set readfds;
                FD_ZERO(&readfds);
                FD_SET(fd, &readfds);

                struct timeval timeout;
                timeout.tv_sec = 1;
                timeout.tv_usec = 0;

                int result = select(fd + 1, &readfds, nullptr, nullptr, &timeout);
                
                if (result > 0 && FD_ISSET(fd, &readfds)) {
                    std::cout << "检测到串口 " << portPath << " 有数据交互" << std::endl;
                    
                    // 读取1字节清空缓冲区，避免持续触发
                    char dummy;
                    read(fd, &dummy, 1);
                    
                    // 短暂延迟避免频繁打印
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
                
                if (result < 0) {
                    break; // 出错，重新打开
                }
            }

            close(fd);
        }
    }

    void start() {
        if (running) return;
        
        running = true;
        std::cout << "启动串口监控..." << std::endl;
        
        for (size_t i = 0; i < serialPorts.size(); ++i) {
            monitorThreads.emplace_back([this, i]() {
                monitorPort(serialPorts[i], i);
            });
        }
    }

    void stop() {
        if (!running) return;
        
        running = false;
        
        for (auto& thread : monitorThreads) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        
        monitorThreads.clear();
    }

    bool isRunning() const { return running; }
};

std::unique_ptr<SimpleSerialMonitor> g_monitor;

void signalHandler(int signal) {
    std::cout << "\n收到退出信号，正在停止..." << std::endl;
    if (g_monitor) {
        g_monitor->stop();
    }
    exit(0);
}

int main() {
    std::cout << "简单串口监控程序启动" << std::endl;
    
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    g_monitor = std::make_unique<SimpleSerialMonitor>();
    g_monitor->start();

    std::cout << "程序运行中，按 Ctrl+C 退出" << std::endl;

    while (g_monitor->isRunning()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    return 0;
}
