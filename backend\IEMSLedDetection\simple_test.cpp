#include <iostream>
#include <unistd.h>
#include <fcntl.h>
#include <sys/select.h>
#include <cstring>
#include <termios.h>

// 简单的串口监控测试程序
int main(int argc, char* argv[]) {
    const char* portPath = "/dev/ttymxc1";
    
    if (argc > 1) {
        portPath = argv[1];
    }
    
    std::cout << "测试监控串口: " << portPath << std::endl;
    
    // 检查串口是否存在
    if (access(portPath, F_OK) != 0) {
        std::cout << "错误: 串口 " << portPath << " 不存在" << std::endl;
        return 1;
    }
    
    std::cout << "串口存在，尝试打开..." << std::endl;
    
    // 打开串口
    int fd = open(portPath, O_RDONLY | O_NONBLOCK);
    if (fd < 0) {
        std::cout << "错误: 无法打开串口 " << portPath << ": " << strerror(errno) << std::endl;
        return 1;
    }
    
    std::cout << "串口打开成功，文件描述符: " << fd << std::endl;
    
    // 获取并显示串口配置
    struct termios tty;
    if (tcgetattr(fd, &tty) == 0) {
        std::cout << "当前串口配置获取成功" << std::endl;
        
        // 设置为原始模式
        cfmakeraw(&tty);
        if (tcsetattr(fd, TCSANOW, &tty) == 0) {
            std::cout << "串口配置为原始模式成功" << std::endl;
        } else {
            std::cout << "警告: 无法设置串口为原始模式: " << strerror(errno) << std::endl;
        }
    } else {
        std::cout << "警告: 无法获取串口配置: " << strerror(errno) << std::endl;
    }
    
    std::cout << "开始监控数据活动..." << std::endl;
    std::cout << "请在另一个终端向串口发送数据，例如:" << std::endl;
    std::cout << "  echo 'test data' > " << portPath << std::endl;
    std::cout << "按 Ctrl+C 退出" << std::endl;
    std::cout << "----------------------------------------" << std::endl;
    
    int dataCount = 0;
    
    while (true) {
        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(fd, &readfds);
        
        struct timeval timeout;
        timeout.tv_sec = 2;  // 2秒超时
        timeout.tv_usec = 0;
        
        std::cout << "等待数据..." << std::endl;
        
        int result = select(fd + 1, &readfds, nullptr, nullptr, &timeout);
        
        if (result < 0) {
            if (errno == EINTR) {
                std::cout << "被信号中断，继续..." << std::endl;
                continue;
            }
            std::cout << "select错误: " << strerror(errno) << std::endl;
            break;
        } else if (result == 0) {
            std::cout << "超时，没有数据活动" << std::endl;
            continue;
        }
        
        if (FD_ISSET(fd, &readfds)) {
            std::cout << "*** 检测到数据活动! ***" << std::endl;
            
            // 尝试读取数据（非侵入式：只读取少量数据用于测试）
            char buffer[64];
            ssize_t bytesRead = read(fd, buffer, sizeof(buffer) - 1);
            
            if (bytesRead > 0) {
                buffer[bytesRead] = '\0';
                dataCount++;
                std::cout << "读取到 " << bytesRead << " 字节数据: ";
                
                // 显示数据（处理不可打印字符）
                for (ssize_t i = 0; i < bytesRead; i++) {
                    if (buffer[i] >= 32 && buffer[i] <= 126) {
                        std::cout << buffer[i];
                    } else {
                        std::cout << "[0x" << std::hex << (unsigned char)buffer[i] << std::dec << "]";
                    }
                }
                std::cout << std::endl;
                std::cout << "总共检测到 " << dataCount << " 次数据传输" << std::endl;
                
            } else if (bytesRead == 0) {
                std::cout << "读取到0字节（EOF）" << std::endl;
            } else {
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    std::cout << "数据暂时不可用（EAGAIN/EWOULDBLOCK）" << std::endl;
                } else {
                    std::cout << "读取错误: " << strerror(errno) << std::endl;
                }
            }
            
            std::cout << "----------------------------------------" << std::endl;
        }
    }
    
    close(fd);
    std::cout << "串口已关闭" << std::endl;
    return 0;
}
